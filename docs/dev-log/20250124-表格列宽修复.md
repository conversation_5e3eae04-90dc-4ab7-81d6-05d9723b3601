# 表格列宽修复

## 问题描述

在 `src/pages/ticket/content/ticketList/content.tsx` 的表格中发现两个列宽问题：

1. **第一列选择框太宽**：选择框列占用了过多的空间
2. **ID列显示不完整**：当ID位数较多时，由于列宽太小（18px）导致内容显示不清楚

## 问题根源分析

### 1. 选择框列宽度问题

- `useDeleteHooks` 中的 `rowSelection` 配置没有设置 `width` 属性
- Semi UI 默认给选择框列分配了较大的宽度
- 根据 Semi UI 文档，`rowSelection` 支持 `width` 属性来控制选择框列宽度

### 2. ID列宽度问题

- ID列设置的 `width: 18` 过小，无法显示完整的ID内容
- 在固定列场景下，过小的宽度会导致内容被截断

### 3. scrollX计算不准确

- 当前的 `scollX` 计算只考虑了 `columns` 中设置的宽度
- 没有考虑选择框列的宽度，导致表格实际宽度与 `scrollX` 不匹配

## 修复方案

### 1. 修改 useDeleteHooks.tsx

为 `rowSelection` 添加 `width` 属性：

```typescript
const rowSelection = useMemo(
  () => ({
    fixed: true,
    width: 50, // 设置选择框列宽度为50px
    onChange: (selectedRowKeys) => {
      setRows(selectedRowKeys);
    },
  }),
  [setRows]
);
```

同时更新了 TypeScript 类型定义以确保类型安全。

### 2. 修改 ticketList/content.tsx

#### 增加ID列宽度

将ID列的宽度从 `18` 增加到 `80`：

```typescript
{
  title: "ID",
  dataIndex: "id",
  fixed: true,
  width: 80, // 增加ID列宽度以显示完整内容
},
```

#### 优化scrollX计算

修改 `scollX` 计算逻辑，考虑选择框列的宽度：

```typescript
const scollX = useMemo(() => {
  const sums: number[] = [];
  // 添加选择框列的宽度（如果不是只读模式且不是modal模式）
  if (mode !== "modal" && !readonly) {
    sums.push(50); // 选择框列宽度
  }
  columns.forEach((o: any) => {
    sums.push(o?.width ?? 0);
  });
  return sum(sums);
}, [columns, mode, readonly]);
```

## 修复效果

### 第一次修复

1. **选择框列宽度合理**：从默认的较大宽度缩小到50px，节省空间
2. **ID列显示完整**：从18px增加到80px，可以显示更长的ID内容
3. **表格滚动对齐**：scrollX计算准确，固定列在滚动时对齐正确

### 第二次优化（基于用户反馈）

1. **选择框列宽度进一步优化**：从50px缩小到32px，更适合复选框的实际大小
2. **ID列宽度调整**：从80px调整到50px，适合3-4位数ID的显示需求
3. **选择框固定功能确认**：确保选择框列设置了`fixed: true`，在表格滚动时保持可选择状态

### 第三次优化（代码规范化）

1. **引入常量管理**：在`useDeleteHooks.tsx`中定义`TABLE_COLUMN_WIDTHS`常量，避免魔法数字
2. **ID列宽度再次调整**：从50px调整到32px，进一步优化空间利用
3. **代码结构优化**：统一使用常量引用，提高代码可维护性

#### 新增常量定义

```typescript
// 表格列宽常量
export const TABLE_COLUMN_WIDTHS = {
  SELECTION: 32, // 选择框列宽度
  ID: 32, // ID列宽度
} as const;
```

#### 修改文件列表

1. **useDeleteHooks.tsx**：

   - 添加`TABLE_COLUMN_WIDTHS`常量定义
   - 更新`rowSelection`配置使用常量
   - 调整`fixed`属性位置

2. **ticketList/content.tsx**：
   - 导入`TABLE_COLUMN_WIDTHS`常量
   - 更新两个ID列配置使用常量
   - 更新`scollX`计算使用常量

## 验证结果

修复后的表格应该具有以下特征：

1. **选择框列宽度合理**：32px宽度，既能完整显示复选框，又不占用过多空间
2. **ID列显示完整**：32px宽度能够完整显示3位数的ID（如果需要显示更多位数可调整常量）
3. **scrollX计算准确**：表格的实际宽度与scrollX设置匹配，避免出现不必要的滚动条
4. **选择框固定**：在表格水平滚动时，选择框列保持固定在左侧
5. **代码可维护性**：使用常量管理列宽，便于统一调整和维护

## 技术要点

- 使用 Semi UI 的 `rowSelection.width` 属性控制选择框列宽度
- 合理设置固定列的宽度，确保内容完整显示
- 准确计算 `scrollX`，考虑所有列（包括选择框列）的宽度
- 更新 TypeScript 类型定义确保类型安全

## 参考文档

- [Semi UI Table 文档](https://semi.design/zh-CN/show/table)
- Semi UI rowSelection 配置项说明
