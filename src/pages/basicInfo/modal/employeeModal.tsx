import { employeeEditModal, employeeFn<PERSON>tom } from "@atoms/basicInfo";
import { Col, Form, Modal, Row, Toast } from "@douyinfe/semi-ui";
import { useMutation, useQuery } from "@tanstack/react-query";
import { createEmployee, getEmployee, updateEmployee } from "api/basicInfo";
import {
  DEGREE_MAP,
  DepartmentSearch,
  EDUCATION_MAP,
  POLITICAL_STATUS_MAP,
  PositionSearch,
  RoleSearch,
  STATUS_MAP,
  Upload,
} from "components";
import { Draft, DraftTrigger, destroyDraft } from "components/Draft";
import { useAtom } from "jotai";
import { omit } from "ramda";
import { useCallback, useEffect, useRef } from "react";
import { filterEditData, formatRFC3339, semiPhoneValidator, semiIdCardValidator } from "utils";

export const EmployeeModal = () => {
  const getFormApiRef = useRef<any>(null);
  const [employeeEdit, setEmployeeEdit] = useAtom(employeeEditModal);
  const [employeeFn] = useAtom(employeeFnAtom);

  const { isLoading, data } = useQuery({
    queryKey: [`em-${employeeEdit?.id ?? ""}`],
    queryFn: () => {
      if (employeeEdit?.id) {
        return getEmployee(employeeEdit?.id);
      }
    },
    enabled: !!employeeEdit?.id,
  });
  const mutation = useMutation({
    mutationFn: employeeEdit?.id ? updateEmployee : createEmployee,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        employeeFn?.refetch?.();
        getFormApiRef.current?.reset?.();
        getFormApiRef.current?.setValues?.({}, { isOverride: true });
        destroyDraft("employeeEdit");
        setEmployeeEdit({
          id: "",
          show: false,
        });
      }
    },
  });

  // 自动填充表单
  useEffect(() => {
    if (employeeEdit?.id && data?.data?.name && getFormApiRef.current) {
      const items = omit(["position", "role", "department"], data?.data);
      const filteredData = filterEditData(items);
      const cleanedData = {
        ...filteredData,
        // 确保图片/附件等字段数据来源正确，但不覆盖 _upload 字段
        // user-defined code here
        picture: items.picture || [],
        // 保留 filterEditData 创建的 _upload 字段
        // picture_upload 已经由 filterEditData 正确处理
      };
      getFormApiRef.current.setValues({
        ...cleanedData,
        positionId: data?.data?.position?.id ?? null,
        roleId: data?.data?.role?.id ?? null,
        departmentId: data?.data?.department?.id ?? null,
      });
    } else {
      getFormApiRef?.current?.reset?.();
      getFormApiRef.current?.setValues?.({}, { isOverride: true });
    }
  }, [employeeEdit?.id, data, getFormApiRef]);

  // 组件卸载时重置表单，清空所有表单数据
  useEffect(() => {
    return () => {
      getFormApiRef?.current?.reset?.();
      getFormApiRef.current?.setValues?.({}, { isOverride: true });
      destroyDraft("employeeEdit");
    };
  }, []);

  const handleClose = useCallback(() => {
    if (mutation.isLoading) {
      return;
    }
    getFormApiRef?.current?.reset?.();
    getFormApiRef.current?.setValues?.({}, { isOverride: true });
    destroyDraft("employeeEdit");
    setEmployeeEdit({
      id: "",
      show: false,
    });
  }, [setEmployeeEdit, mutation]);

  const handleSetFormApi = useCallback(
    (formApi) => {
      getFormApiRef.current = formApi;
    },
    [getFormApiRef]
  );

  const handleOk = () => {
    if (mutation.isLoading) {
      return;
    }
    getFormApiRef.current
      .validate()
      .then((values) => {
        let obj = {
          ...values,
          departmentId: parseInt(values?.departmentId ?? "0"),
          roleId: parseInt(values?.roleId ?? "0"),
          employmentDate: formatRFC3339(values?.employmentDate ?? new Date()),
        };
        if (employeeEdit?.id) {
          mutation.mutate({
            id: employeeEdit?.id,
            values: obj,
          });
        } else {
          mutation.mutate(obj);
        }
      })
      .catch((errors) => {
        console.log(errors);
        const [formFieldId] = Object.keys(errors || {});
        if (formFieldId) {
          document.getElementById(formFieldId)?.scrollIntoViewIfNeeded?.();
        }
      });
  };
  const rules = [{ required: true, message: "此为必填项!" }];
  const title = employeeEdit?.id ? "编辑员工资料" : "新增员工";
  // 手机号校验规则（复用公共校验器）
  const phoneRules = [
    { required: true, message: "请输入手机号" },
    { validator: semiPhoneValidator, message: "请输入有效的手机号码" },
  ];

  return (
    <>
      <DraftTrigger id="employeeEdit" draftAtom={employeeEditModal} />
      <Modal
        title={title}
        visible={employeeEdit?.show ?? false}
        onCancel={handleClose}
        maskClosable={false}
        keepDOM
        width={800}
        footer={
          <div className="flex gap-2 justify-end">
            <button className="btn rounded btn-sm" onClick={handleClose}>
              取消
            </button>
            {mutation.isLoading ? (
              <button className="btn rounded btn-primary btn-sm btn-disabled">
                <span className="loading loading-spinner loading-xs"></span>
                确定
              </button>
            ) : (
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={handleOk}
              >
                确定
              </button>
            )}
          </div>
        }
        centered
      >
        <Form
          labelPosition="left"
          labelAlign="right"
          labelWidth={100}
          getFormApi={handleSetFormApi}
        >
          <Form.Section text={"基本信息"}>
            <Row gutter={20}>
              <Col span={12}>
                <Form.Input
                  field="name"
                  label="姓名"
                  trigger="blur"
                  rules={rules}
                />
              </Col>
              <Col span={12}>
                <Form.Select
                  field="gender"
                  label="性别"
                  className="w-full"
                  rules={rules}
                >
                  <Form.Select.Option value={1}>男</Form.Select.Option>
                  <Form.Select.Option value={2}>女</Form.Select.Option>
                </Form.Select>
              </Col>
              <Col span={12}>
                <DepartmentSearch
                  field="departmentId"
                  label="所属部门"
                  placeholder=""
                  isRequired
                />
              </Col>
              <Col span={12}>
                <PositionSearch
                  field="positionId"
                  label="岗位"
                  placeholder=""
                  isRequired
                />
              </Col>
              <Col span={12}>
                <RoleSearch
                  field="roleId"
                  label="角色"
                  placeholder=""
                  isRequired
                />
              </Col>
              <Col span={12}>
                <Form.Input
                  field="employeeId"
                  label="工号"
                  trigger="blur"
                  rules={rules}
                />
              </Col>
              <Col span={12}>
                <Form.Select
                  field="status"
                  label="在职状态"
                  className="w-full"
                  rules={rules}
                >
                  {STATUS_MAP.map((o) => (
                    <Form.Select.Option value={o.id} key={o.id}>
                      {o.name}
                    </Form.Select.Option>
                  ))}
                </Form.Select>
              </Col>
              <Col span={12}>
                <Form.Input
                  field="mobile"
                  label="手机号"
                  trigger="blur"
                  rules={phoneRules}
                  maxLength={11}
                />
              </Col>
            </Row>
          </Form.Section>

          <Form.Section text={"其他信息"}>
            <Row gutter={20}>
              <Col span={12}>
                <Form.DatePicker
                  field="employmentDate"
                  label="入职时间"
                  className="w-full"
                  initValue={new Date()}
                ></Form.DatePicker>
              </Col>
              <Col span={12}>
                <Form.Input label="固定电话" field="landline" trigger="blur" />
              </Col>
              <Col span={12}>
                <Form.Select field="education" label="学历" className="w-full">
                  {EDUCATION_MAP.map((o) => (
                    <Form.Select.Option value={o.id} key={o.id}>
                      {o.name}
                    </Form.Select.Option>
                  ))}
                </Form.Select>
              </Col>
              <Col span={12}>
                <Form.Select field="degree" label="学位" className="w-full">
                  {DEGREE_MAP.map((o) => (
                    <Form.Select.Option value={o.id} key={o.id}>
                      {o.name}
                    </Form.Select.Option>
                  ))}
                </Form.Select>
              </Col>
              <Col span={12}>
                <Form.Input field="major" label="专业" />
              </Col>
              <Col span={12}>
                <Form.Input field="school" label="毕业院校" />
              </Col>
              <Col span={12}>
                <Form.Input
                  field="idNumber"
                  label="身份证号"
                  trigger="blur"
                  rules={[{ validator: semiIdCardValidator, message: "身份证号格式或校验位不正确" }]}
                  maxLength={18}
                />
              </Col>
              <Col span={12}>
                <Form.DatePicker
                  label="出生日期"
                  field="birthDate"
                  className="w-full"
                  // rules={rules}
                />
              </Col>
              <Col span={12}>
                <Form.Select
                  field="politicalStatus"
                  label="政治面貌"
                  className="w-full"
                >
                  {POLITICAL_STATUS_MAP.map((o) => (
                    <Form.Select.Option value={o.id} key={o.id}>
                      {o.name}
                    </Form.Select.Option>
                  ))}
                </Form.Select>
              </Col>
              <Col span={12}>
                <Form.Input field="nativePlace" label="籍贯" />
              </Col>
              <Col span={12}>
                <Form.Input field="nation" label="民族" />
              </Col>
              <Col span={12}>
                <Form.Input label="邮箱" field="email" trigger="blur" />
              </Col>
            </Row>
            <Row gutter={20}>
              <Col span={24}>
                <Upload
                  field="picture_upload"
                  formField="picture"
                  label="个人照"
                />
              </Col>
            </Row>
          </Form.Section>

          {employeeEdit?.id ? null : (
            <Draft id="employeeEdit" draftAtom={employeeEditModal} />
          )}
        </Form>
      </Modal>
    </>
  );
};
