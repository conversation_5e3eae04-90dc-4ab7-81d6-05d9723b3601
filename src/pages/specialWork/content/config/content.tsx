import { Col, Collapse, Form, InputGroup, Row, Toast } from "@douyinfe/semi-ui";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { getJobSlice, getSpecialWorkConfig, specialWorkConfigApis } from "api";
import { specialWorkConfigAtoms } from "atoms";
import {
  EmployeeSearch,
  highLevelForm,
  IS_ISNOT_MAP,
  JOB_APPOINTMENT_APPROVE_TYPE_MAP,
  PERSON_IDENTIFICATIONMODE_MAP,
} from "components";
import { find, omit, pick, propEq } from "ramda";
import { useEffect, useRef } from "react";
import { filterEditData } from "utils";
import { requiredRule } from "utils/constants";

export const SpecialWorkConfigContent = ({
  callback,
  layout,
  referAtom,
  readonly = false,
  filter = {},
  ...restProps
}) => {
  const gutter = 24;

  const queryClient = useQueryClient();
  const queryKey = "list" + specialWorkConfigAtoms.entity;

  const formRef = useRef<any>(null);

  const { isLoading, data } = useQuery({
    queryKey: [queryKey],
    queryFn: () => {
      return getSpecialWorkConfig();
    },
  });

  const { data: jobs } = useQuery({
    queryKey: ["getJobSlice"],
    queryFn: getJobSlice,
  });

  useEffect(() => {
    if (data && formRef.current?.formApi) {
      const items = omit([], data?.data);

      formRef.current?.formApi.setValues(
        {
          ...filterEditData(items),
        },
        { isOverride: true }
      );
    } else {
      formRef.current?.frormApi?.reset?.();
    }
  }, [data, formRef.current?.formApi]);

  const specialWorkConfigMutation = useMutation({
    mutationFn: specialWorkConfigApis.create,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
      }
    },
  });

  const findText = (val: number) => {
    const name =
      find(propEq(val, "highLevel"))(highLevelForm)?.highLevelName ?? "";
    return `${name}高处作业高度`;
  };

  const validateSingleRule = (rule, index) => {
    if (!rule?.rangeRuleList?.[0] || !rule?.rangeRuleList?.[0]?.pivotNumber) {
      return { isValid: true };
    }

    // 如果只有一个端点，则验证通过
    if (!rule?.rangeRuleList?.[1]?.pivotNumber) {
      return { isValid: true };
    }

    const start = Number(rule.rangeRuleList[0].pivotNumber);
    const end = Number(rule.rangeRuleList[1].pivotNumber);
    const startOp = rule.rangeRuleList[0].operator;
    const endOp = rule.rangeRuleList[1].operator;

    // 检查是否形成有效区间
    if (startOp === 4 || startOp === 2 || startOp === 5) {
      if (endOp === 3 || endOp === 1) {
        if (start < end) {
          return { isValid: true };
        } else {
          return {
            isValid: false,
            message: `${findText(index + 1)} 无效: 起始值应小于结束值。`,
          };
        }
      }
    }

    return {
      isValid: false,
      message: `${findText(index + 1)} 无效: 操作符组合不正确。`,
    };
  };

  const validateRulesOverlap = (rules) => {
    const validRules = rules.filter(
      (rule) =>
        rule?.rangeRuleList?.[0]?.pivotNumber &&
        rule?.rangeRuleList?.[0]?.operator
    );

    for (let i = 0; i < validRules.length; i++) {
      for (let j = i + 1; j < validRules.length; j++) {
        const currentRule = validRules[i];
        const nextRule = validRules[j];

        const currentStart = Number(currentRule.rangeRuleList[0].pivotNumber);
        const nextStart = Number(nextRule.rangeRuleList[0].pivotNumber);

        const currentEnd = currentRule.rangeRuleList[1]?.pivotNumber
          ? Number(currentRule.rangeRuleList[1].pivotNumber)
          : Infinity;
        const nextEnd = nextRule.rangeRuleList[1]?.pivotNumber
          ? Number(nextRule.rangeRuleList[1].pivotNumber)
          : Infinity;

        // 检查区间是否有重叠
        if (!(currentEnd <= nextStart)) {
          return {
            isValid: false,
            message: `${findText(i + 1)} 和 ${findText(j + 1)} 之间存在重叠区间，请检查。`,
          };
        }
      }
    }
    return { isValid: true };
  };

  const handleSubmit = (values) => {
    const formValues = formRef.current?.formApi.getValues();
    const rules = formValues.highWorkHeightRuleList || [];

    // 验证每个单独的规则
    const invalidRules = rules
      .map((rule, index) => validateSingleRule(rule, index))
      .filter((result) => !result.isValid);
    if (invalidRules.length > 0) {
      invalidRules.forEach((rule) => {
        Toast.error({
          content: rule.message,
          duration: 3,
        });
      });
      return;
    }

    // 验证规则之间是否有重叠
    const overlapValidation = validateRulesOverlap(rules);
    if (!overlapValidation.isValid) {
      Toast.error({
        content: overlapValidation.message,
        duration: 3,
      });
      return;
    }
    const data = pick(
      [
        "appointmentApproveType",
        "identificationMode",
        "mustInElectron",
        "mustWithVideo",
        "mustWithCoodination",
        "firstJobCategoryId",
        "needCertificateWorkerTypeList",
        "jobInspectionValidHours",
        "acceptLimitMinutes",
        "acceptNoticeMinutes",
        "supervisorIdList",
      ],
      formValues
    );
    const _highWorkHeightRuleList: any[] = [];
    formValues?.highWorkHeightRuleList?.forEach((o: any, i: number) => {
      const rangeRuleList = [];
      o.rangeRuleList.forEach((o: any) => {
        rangeRuleList.push({
          operator: o.operator,
          pivotNumber: parseInt(o.pivotNumber),
        });
      });
      _highWorkHeightRuleList.push({
        highLevel: i + 1,
        rangeRuleList: rangeRuleList,
      });
    });

    specialWorkConfigMutation.mutate({
      ...data,
      highWorkHeightRuleList: _highWorkHeightRuleList,
    });
  };

  return (
    <div className="bg-white shadow p-4 h-fit rounded">
      <Form
        labelPosition="left"
        labelAlign="right"
        labelWidth={260}
        ref={formRef}
        onSubmit={(values) => handleSubmit(values)}
        autoScrollToError
      >
        {({ formState }) => (
          <>
            <Row gutter={gutter}>
              <Col span={12}>
                <Form.RadioGroup
                  field="identificationMode"
                  label="人员确认身份方式"
                  rules={[{ required: true, message: "此为必填项" }]}
                >
                  {PERSON_IDENTIFICATIONMODE_MAP.map((item) => (
                    <Form.Radio key={item.id} value={item.id}>
                      {item.name}
                    </Form.Radio>
                  ))}
                </Form.RadioGroup>
              </Col>
            </Row>
            <Row gutter={gutter}>
              <Col span={12}>
                <Form.Select
                  label="作业预约审批方式"
                  field="appointmentApproveType"
                  className="w-[200px]"
                  rules={[{ required: true, message: "此为必填项" }]}
                >
                  {JOB_APPOINTMENT_APPROVE_TYPE_MAP.map((item) => (
                    <Form.Select.Option key={item.id} value={item.id}>
                      {item.name}
                    </Form.Select.Option>
                  ))}
                </Form.Select>
              </Col>
            </Row>
            <Row gutter={gutter}>
              <Col span={12}>
                <Form.RadioGroup
                  label="作业流程是否必须在电子围栏内"
                  field="mustInElectron"
                  rules={[{ required: true, message: "此为必填项" }]}
                >
                  {IS_ISNOT_MAP.map((item) => (
                    <Form.Radio key={item.id} value={item.id}>
                      {item.name}
                    </Form.Radio>
                  ))}
                </Form.RadioGroup>
              </Col>
            </Row>
            <Row gutter={gutter}>
              <Col span={12}>
                <Form.RadioGroup
                  label="作业开票必须绑定监控"
                  field="mustWithVideo"
                  rules={[{ required: true, message: "此为必填项" }]}
                >
                  {IS_ISNOT_MAP.map((item) => (
                    <Form.Radio key={item.id} value={item.id}>
                      {item.name}
                    </Form.Radio>
                  ))}
                </Form.RadioGroup>
              </Col>
            </Row>
            <Row gutter={gutter}>
              <Col span={12}>
                <Form.RadioGroup
                  label="作业开票必须选择定位"
                  field="mustWithCoodination"
                  rules={[{ required: true, message: "此为必填项" }]}
                >
                  {IS_ISNOT_MAP.map((item) => (
                    <Form.Radio key={item.id} value={item.id}>
                      {item.name}
                    </Form.Radio>
                  ))}
                </Form.RadioGroup>
              </Col>
            </Row>
            <Row gutter={gutter}>
              <Col span={12}>
                <Form.Select
                  label="主票作业类型"
                  field="firstJobCategoryId"
                  className="w-[200px]"
                  rules={[{ required: true, message: "此为必填项" }]}
                >
                  <Form.Select.Option value={0}>无</Form.Select.Option>
                  {(jobs?.data ?? [])?.map?.((item: any) => (
                    <Form.Select.Option
                      key={item.category.id}
                      value={item.category.id}
                    >
                      {item.category.name}
                    </Form.Select.Option>
                  ))}
                </Form.Select>
              </Col>
            </Row>
            <Row gutter={gutter}>
              <Col span={gutter}>
                <Form.CheckboxGroup
                  label="必选持证作业人的作业类型"
                  field="needCertificateWorkerTypeList"
                  rules={[{ required: true, message: "请至少选择一项" }]}
                  direction="horizontal"
                  style={{ display: "flex", flexWrap: "wrap", gap: "16px" }}
                >
                  {(jobs?.data ?? [])
                    .filter((job: any) => job.category.isSpecial === 1)
                    .map((item: any) => (
                      <Form.Checkbox
                        key={item.category.id}
                        value={item.category.id}
                        style={{ minWidth: "120px", marginRight: "8px" }}
                      >
                        {item.category.name}
                      </Form.Checkbox>
                    ))}
                </Form.CheckboxGroup>
              </Col>
            </Row>
            <Row gutter={gutter}>
              <Col span={12}>
                <Form.InputNumber
                  placeholder="请输入时间"
                  field={`jobInspectionValidHours`}
                  step={1}
                  label="作业巡检最长时间"
                  suffix={"小时"}
                  rules={[{ required: true, message: "此为必填项" }]}
                />
              </Col>
            </Row>
            <Row gutter={gutter}>
              <Col span={24}>
                <div className="flex items-center">
                  <div
                    className="w-[260px] text-right pr-2 flex-shrink-0"
                    style={{
                      fontSize: "14px",
                      color: "var(--semi-color-text-1)",
                      fontWeight: 600,
                      lineHeight: "20px",
                    }}
                  >
                    作业验收规则
                    <span
                      style={{
                        color: "var(--semi-color-danger)",
                        marginRight: "4px",
                      }}
                    >
                      *
                    </span>
                  </div>
                  <div
                    className="flex items-center flex-nowrap whitespace-nowrap"
                    style={{
                      fontSize: "14px",
                      color: "var(--semi-color-text-0)",
                      lineHeight: "20px",
                    }}
                  >
                    验收需在作业完成后（
                    <Form.InputNumber
                      field="acceptLimitMinutes"
                      trigger="blur"
                      rules={[requiredRule]}
                      min={0}
                      style={{
                        width: "48px",
                        // margin: "0 5px",
                        display: "inline-block",
                      }}
                      noLabel
                    />
                    分钟）内完成，验收时间到期前（
                    <Form.InputNumber
                      field="acceptNoticeMinutes"
                      trigger="blur"
                      rules={[requiredRule]}
                      min={0}
                      style={{
                        width: "48px",
                        // margin: "0 5px",
                        display: "inline-block",
                      }}
                      noLabel
                    />
                    分钟）进行提醒
                  </div>
                </div>
              </Col>
            </Row>
            <Row gutter={gutter}>
              <Col span={12}>
                <EmployeeSearch
                  label="作业过程监督人员"
                  field="supervisorIdList"
                  placeholder="请选择作业过程监督人员"
                  multiple
                  // isRequired
                />
              </Col>
            </Row>
            <Collapse className="px-20" defaultActiveKey="1">
              <Collapse.Panel header="高处作业高度等级标准" itemKey="1">
                <Row gutter={1}>
                  <Col span={24}>
                    {highLevelForm.map((o, i) => (
                      <div className="flex items-center" key={i}>
                        <InputGroup>
                          <Form.Select
                            field={`highWorkHeightRuleList[${i}].rangeRuleList[0].operator`}
                            label={`${o.highLevelName}高处作业高度`}
                            placeholder="条件"
                            rules={[{ required: true, message: "此为必填项" }]}
                          >
                            <Form.Select.Option value={1}>
                              &lt;
                            </Form.Select.Option>
                            <Form.Select.Option value={2}>
                              &gt;
                            </Form.Select.Option>
                            <Form.Select.Option value={3}>
                              &le;
                            </Form.Select.Option>
                            <Form.Select.Option value={4}>
                              &ge;
                            </Form.Select.Option>
                            <Form.Select.Option value={5}>=</Form.Select.Option>
                          </Form.Select>
                          <Form.InputNumber
                            placeholder="请输入高度"
                            // className="w-12"
                            field={`highWorkHeightRuleList[${i}].rangeRuleList[0].pivotNumber`}
                            noLabel
                            rules={[{ required: true, message: "此为必填项" }]}
                          />
                        </InputGroup>
                        <span className="mx-4 text-base">至</span>
                        <InputGroup>
                          <Form.Select
                            field={`highWorkHeightRuleList[${i}].rangeRuleList[1].operator`}
                            rules={[
                              {
                                required: !Boolean(
                                  highLevelForm.length - 1 === i
                                ),
                                message: "此为必填项",
                              },
                            ]}
                            placeholder="条件"
                            noLabel
                          >
                            <Form.Select.Option value={1}>
                              &lt;
                            </Form.Select.Option>
                            <Form.Select.Option value={2}>
                              &gt;
                            </Form.Select.Option>
                            <Form.Select.Option value={3}>
                              &le;
                            </Form.Select.Option>
                            <Form.Select.Option value={4}>
                              &ge;
                            </Form.Select.Option>
                            <Form.Select.Option value={5}>=</Form.Select.Option>
                          </Form.Select>
                          <Form.InputNumber
                            placeholder="请输入高度"
                            // className="w-12"
                            field={`highWorkHeightRuleList[${i}].rangeRuleList[1].pivotNumber`}
                            noLabel
                            rules={[
                              {
                                required: !Boolean(
                                  highLevelForm.length - 1 === i
                                ),
                                message: "此为必填项",
                              },
                            ]}
                          />
                        </InputGroup>
                      </div>
                    ))}
                  </Col>
                </Row>
              </Collapse.Panel>
            </Collapse>
            <div className="flex gap-2 justify-end">
              <button className="btn btn-primary btn-sm rounded w-[100px]">
                提交
              </button>
            </div>
          </>
        )}
      </Form>
    </div>
  );
};
