import { Col, Form, Modal, Row, Toast } from "@douyinfe/semi-ui";
import { useCallback, useEffect, useRef } from "react";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { enterpriseCertificateApis } from "api/basicInfo";
import { enterpriseCertificateAtoms } from "atoms";
import { Upload } from "components";
import { Draft, DraftTrigger, destroyDraft } from "components/Draft";
import { useAtom } from "jotai";
import { omit } from "ramda";
import { useNavigate } from "react-router-dom";
import { filterEditData } from "utils";

export const EnterpriseCertificateModal = () => {
  const operation = "Edit";
  const newTitle = "新增企业证书信息"; //user-defined code here
  const editTitle = "编企业证书信息"; //user-defined code here
  const ruleMessage = ["此为必填项!"];

  const atoms = enterpriseCertificateAtoms;
  const apis = enterpriseCertificateApis;

  const entity = atoms.entity;
  const uniqueKey = `${entity}${operation}`;

  const [editModalAtom, setEditModalAtom] = useAtom(atoms.editModal);
  const [fnAtom] = useAtom(atoms.Fn);

  const title = editModalAtom?.id ? editTitle : newTitle;
  const rules = [{ required: true, message: ruleMessage[0] }];

  const queryClient = useQueryClient();
  const queryKey = "list" + entity;

  const getFormApiRef = useRef<any>(null);
  const navigate = useNavigate();

  const { isLoading, data } = useQuery({
    queryKey: [`${entity}-${editModalAtom?.id ?? ""}`],
    queryFn: () => {
      if (editModalAtom?.id) {
        return apis.get(editModalAtom?.id);
      }
    },
    enabled: !!editModalAtom?.id,
  });
  const mutation = useMutation({
    mutationFn: editModalAtom?.id ? apis.update : apis.create,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        fnAtom?.refetch?.();
        destroyDraft(uniqueKey);
        getFormApiRef.current?.reset?.();
        getFormApiRef.current?.setValues?.({}, { isOverride: true });
        setEditModalAtom({
          id: "",
          show: false,
        });
      }
    },
  });

  // 自动填充表单
  useEffect(() => {
    if (editModalAtom?.id && data?.data?.name && getFormApiRef.current) {
      const items = omit([], data?.data);
      const filteredData = filterEditData(items);
      const cleanedData = {
        ...filteredData,
        // 确保图片字段数据来源正确，但不覆盖 _upload 字段
        imageList: items.imageList || [],
        attachmentList: items.attachmentList || [],
        // 保留 filterEditData 创建的 _upload 字段
        // imageList_upload 和 attachmentList_upload 已经由 filterEditData 正确处理
      };
      getFormApiRef.current.setValues(cleanedData, { isOverride: true });
    } else {
      getFormApiRef?.current?.reset?.();
      getFormApiRef.current?.setValues?.({}, { isOverride: true });
    }
  }, [editModalAtom?.id, data, getFormApiRef]);

  // 组件卸载时重置表单，清空所有表单数据
  useEffect(() => {
    return () => {
      getFormApiRef?.current?.reset?.();
      getFormApiRef.current?.setValues?.({}, { isOverride: true });
      destroyDraft(uniqueKey);
    };
  }, []);

  const handleSetFormApi = useCallback(
    (formApi) => {
      getFormApiRef.current = formApi;
    },
    [getFormApiRef]
  );

  const handleClose = useCallback(() => {
    if (mutation.isLoading) {
      return;
    }
    destroyDraft(`${entity}${operation}`);
    setEditModalAtom({
      id: "",
      show: false,
    });
  }, [setEditModalAtom, mutation]);

  const handleOk = () => {
    if (mutation.isLoading) {
      return;
    }
    getFormApiRef.current
      .validate()
      .then((values) => {
        let obj = {
          ...values,
          //user-defined code here
        };
        if (editModalAtom?.id) {
          mutation.mutate({
            id: editModalAtom?.id,
            values: obj,
          });
        } else {
          mutation.mutate(obj);
        }
      })
      .catch((errors) => {
        console.log(errors);
        const [formFieldId] = Object.keys(errors || {});
        if (formFieldId) {
          document.getElementById(formFieldId)?.scrollIntoViewIfNeeded?.();
        }
      });
  };

  return (
    <>
      <DraftTrigger id={uniqueKey} draftAtom={atoms.editModal} />
      <Modal
        title={title}
        visible={editModalAtom?.show ?? false}
        onCancel={handleClose}
        maskClosable={false}
        keepDOM
        width={800}
        footer={
          <div className="flex gap-2 justify-end">
            <button className="btn rounded btn-sm" onClick={handleClose}>
              取消
            </button>
            {mutation.isLoading ? (
              <button className="btn rounded btn-primary btn-sm btn-disabled">
                <span className="loading loading-spinner loading-xs"></span>
                确定
              </button>
            ) : (
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={handleOk}
              >
                确定
              </button>
            )}
          </div>
        }
        centered
      >
        <Form
          labelPosition="left"
          labelAlign="right"
          labelWidth={100}
          getFormApi={handleSetFormApi}
        >
          <Row gutter={20}>
            <Col span={12}>
              <Form.Input
                field="name"
                label="证书名称"
                trigger="blur"
                rules={rules}
              />
            </Col>
            <Col span={12}>
              <Form.Input
                field="code"
                label="证书编号"
                trigger="blur"
                rules={rules}
              />
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <Form.Input
                field="type"
                label="证书类型"
                trigger="blur"
                rules={rules}
              />
            </Col>
            <Col span={12}>
              <Form.DatePicker
                label="发证日期"
                field="issueDate"
                className="w-full"
              />
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <Form.Input field="issueAuthority" label="发证机构" />
            </Col>
            <Col span={12}>
              <Form.DatePicker
                field="expireDate"
                className="w-full"
                label="到期日期"
                rules={rules}
              />
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <Form.TextArea field="content" label="证书描述" />
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Upload
                field="imageList_upload"
                formField="imageList"
                label="图片"
                arrayProcessType="array"
                maxSize={2 * 51200} //KB
              />
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              {/* 图像(.jpg，.png，.gif，.jpeg)，文档(.doc，.docx，.pdf，.xlsx，.xls，.ppt)   大小限制在50M */}
              <Upload
                field="attachmentList_upload"
                formField="attachmentList"
                arrayProcessType="array"
                label="附件"
                type="file"
                listType="list"
                accept=".jpg,.png,.gif,.jpeg,.doc,.docx,.pdf,.xlsx,.xls,.ppt"
                maxSize={2 * 51200} //KB
              />
            </Col>
          </Row>

          {editModalAtom?.id ? null : (
            <Draft id={uniqueKey} draftAtom={atoms.editModal} />
          )}
        </Form>
      </Modal>
    </>
  );
};
