import { getIcon, pickProvinceColor } from "components/region_plugins/utils";
import type React from "react";

import {
  JiangxiConfigContent,
  JiangxiXinganConfigContent,
  ZhejiangConfigContent,
  ZhejiangNingboConfigContent,
  ZhiyuanConfigContent,
} from "./regionPluginConfigForms";

/** 展示 & UI 需要的插件信息 */
export interface RegionPluginInfo {
  readonly name: string;
  readonly description: string;
  readonly icon: React.ReactNode;
  readonly color: string;
  readonly version: string;
  readonly lastUpdate: string;
  readonly enabled?: boolean;
}

/** 元数据项：id + info (+ Form) */
export interface RegionPluginMeta {
  readonly id: number;
  readonly info: RegionPluginInfo;
  /** 具体地区表单组件，必须存在 */
  readonly Form: React.FC;
}

/**
 * 上报类型插件元数据
 * 每个插件包含:
 * - id: 唯一标识符 (1: 浙江, 2: 江西)
 * - info: 插件信息 (名称、描述、图标等)
 * - Form: 对应的表单组件
 */
export const REGION_PLUGIN_META: ReadonlyArray<RegionPluginMeta> = [
  {
    id: 1,
    info: {
      name: "浙江省平台",
      description: "浙江省平台基础信息上报配置",
      icon: getIcon("province"),
      color: pickProvinceColor("Z"),
      version: "1.0.0",
      lastUpdate: "2025-09-09",
    },
    Form: ZhejiangConfigContent,
  },
  {
    id: 2,
    info: {
      name: "江西省平台",
      description: "江西省平台基础信息上报配置",
      icon: getIcon("province"),
      color: pickProvinceColor("J"),
      version: "1.0.0",
      lastUpdate: "2025-09-09",
    },
    Form: JiangxiConfigContent,
  },
  {
    id: 3,
    info: {
      name: "江西新干盐化城",
      description: "江西新干盐化城基础信息上报配置",
      icon: getIcon("zone"),
      color: pickProvinceColor("X"),
      version: "1.0.0",
      lastUpdate: "2025-09-15",
    },
    Form: JiangxiXinganConfigContent,
  },
  {
    id: 4,
    info: {
      name: "浙江宁波园区",
      description: "浙江宁波园区基础信息上报配置",
      icon: getIcon("city"),
      color: pickProvinceColor("N"),
      version: "1.0.0",
      lastUpdate: "2025-09-15",
    },
    Form: ZhejiangNingboConfigContent,
  },
  {
    id: 5,
    info: {
      name: "智园园区",
      description: "智园园区基础信息上报配置",
      icon: getIcon("city"),
      color: pickProvinceColor("Z"),
      version: "1.0.0",
      lastUpdate: "2025-09-23",
    },
    Form: ZhiyuanConfigContent,
  },
] as const;
