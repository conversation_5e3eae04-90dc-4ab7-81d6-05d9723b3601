import {
  IconDownload,
  IconPlus,
  IconPrint,
  IconRefresh,
  IconSetting,
} from "@douyinfe/semi-icons";
import {
  Button,
  ButtonGroup,
  Dropdown,
  Modal,
  Table,
  Tag,
  Tooltip,
} from "@douyinfe/semi-ui";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { delJobSlice, delJobSlices, getJobSliceList } from "api";
import { referJsAtom } from "atoms";
import {
  inspectionModalAtom,
  jobSliceColumnsAtom,
  jobSliceColumnsInContractorAtom,
  jobSliceConfigModalAtom,
  jobSliceFilterAtom,
  jobSliceInfoAtom,
  jsTemplateEditModalAtom,
} from "atoms/specialWork";
import {
  ExportContentType,
  ExportFormat,
  JOB_IS_HIGHRISK_MAP,
  TableConfig,
  useExport,
} from "components";
import {
  TABLE_COLUMN_WIDTHS,
  useBtnHooks,
  useDeleteHooks,
  useHiddenSettings,
} from "hooks";
import { useAtom } from "jotai";
import { useReset<PERSON>tom } from "jotai/utils";
import { find, propEq, sum } from "ramda";
import React, { useCallback, useEffect, useMemo } from "react";
import { useLoaderData, useLocation, useNavigate } from "react-router-dom";
import { InspectionModal } from "./inspectionModal";

export function TicketListContent({
  mode,
  cb,
  filter,
  readonly = false,
  isInContractorModule = false,
}) {
  const loderData = useLoaderData();
  const { pathname, search } = useLocation();
  const genBtn = useBtnHooks(loderData, pathname);
  const navigate = useNavigate();
  const [removeBtn, rowSelection, removesBtn] = useDeleteHooks({
    deleteFn: delJobSlice,
    deletesFn: delJobSlices,
    queryKey: "jobSlicePage",
  });
  const queryClient = useQueryClient();
  const [configModal, setShow] = useAtom(jobSliceConfigModalAtom);
  const [info, setInfo] = useAtom(jobSliceInfoAtom);
  const [_columns, setColumns] = isInContractorModule
    ? useAtom(jobSliceColumnsInContractorAtom)
    : useAtom(jobSliceColumnsAtom);
  const [jobSliceFilter, setjobSliceFilter] = useAtom(jobSliceFilterAtom);
  const [inspection, setInspection] = useAtom(inspectionModalAtom);
  const resetFilter = useResetAtom(jobSliceFilterAtom);
  const [jsTemplateEdit, setJsTemplateEdit] = useAtom(jsTemplateEditModalAtom);
  const reset = useResetAtom(referJsAtom);

  const exportContext = useExport();
  const canExport = exportContext && exportContext.exportToFile;

  const hiddenSettings = useHiddenSettings();

  const { isLoading, data, refetch } = useQuery({
    queryKey: ["jobSlicePage", jobSliceFilter, filter],
    queryFn: () => {
      return getJobSliceList({
        ...jobSliceFilter,
        filter: { ...filter, ...jobSliceFilter.filter },
      });
    },
  });

  useEffect(() => {
    if (mode != "modal" && search !== "?reset=0") {
      resetFilter();
    }
  }, [mode]);

  const dataSource = useMemo(() => {
    return data?.data ?? {};
  }, [data]);
  const result = useMemo(() => dataSource?.results ?? [], [dataSource]);

  const handleExport = (
    format: ExportFormat = "excel",
    contentType: ExportContentType = "list"
  ) => {
    exportContext?.exportToFile?.({
      format: format,
      contentType: contentType,
      // data: data,
      apiFn: getJobSliceList,
      params: { filter: { ...filter, ...jobSliceFilter }, ...jobSliceFilter },
      columns: _columns,
      entityName: "作业票",
    });
  };
  const handleOpenSetting = useCallback(() => {
    setShow(true);
  }, [setShow]);

  const handleOpenInfo = useCallback(
    (item) => {
      setInfo({
        show: true,
        id: item?.id,
      });
    },
    [setInfo]
  );

  const handleOpenInspection = useCallback(
    (item) => {
      setInspection({
        show: true,
        id: item?.id,
      });
    },
    [setInspection]
  );

  const [printId, setPrintId] = React.useState();
  const handleOpenPrint = useCallback((item) => {
    setPrintId(item.id);
  }, []);
  const handleCancelPrint = useCallback(() => {
    setPrintId(null);
  }, []);

  const handleSetRefer = (record: any) => {
    cb?.(record);
    reset();
  };
  const columns1 = [
    {
      title: "ID",
      dataIndex: "id",
      fixed: true,
      width: TABLE_COLUMN_WIDTHS.ID,
    },
    {
      title: "作业票编码",
      dataIndex: "code",
      fixed: true,
      isShow: true,
      width: 64,
      ellipsis: true,
      render: (text) => (
        <Tooltip content={text}>
          <span className="block truncate">{text}</span>
        </Tooltip>
      ),
    },
    {
      title: "作业票名称",
      dataIndex: "name",
      isShow: true,
      ellipsis: true,
      fixed: true,
      width: 48,
      render: (text) => (
        <Tooltip content={text}>
          <span className="block truncate">{text}</span>
        </Tooltip>
      ),
    },
  ];

  const columns2 = [
    {
      title: "是否高风险",
      isShow: true,
      dataIndex: "isHighRisk",
      width: 48,
      render: (text) => {
        const i = find(propEq(text, "id"))(JOB_IS_HIGHRISK_MAP);
        return (
          <Tooltip content={i?.name ?? "-"}>
            <Tag color={i?.color} type="light">
              {i?.name ?? "-"}
            </Tag>
          </Tooltip>
        );
      },
      renderText: (text) => {
        const i = find(propEq(text, "id"))(JOB_IS_HIGHRISK_MAP);
        return i?.name ?? "-";
      },
    },
  ];

  const columns = useMemo(() => {
    if (mode === "modal") {
      return [
        ...columns1,
        ..._columns,
        ...(hiddenSettings?.specialClient === 2 ? columns2 : []),
        {
          title: <Tooltip content="操作">操作</Tooltip>,
          isShow: true,
          dataIndex: "operate",
          key: "operate",
          align: "center",
          fixed: "right",
          width: 40,
          render: (text, record) => (
            <Button
              onClick={() => {
                handleSetRefer(record);
              }}
            >
              引用该票
            </Button>
          ),
        },
      ];
    }

    return [
      {
        title: "ID",
        dataIndex: "id",
        fixed: true,
        width: TABLE_COLUMN_WIDTHS.ID,
      },
      {
        title: "作业票编码",
        dataIndex: "code",
        fixed: true,
        isShow: true,
        width: 64,
        ellipsis: true,
        render: (text) => (
          <Tooltip content={text}>
            <span className="block truncate">{text}</span>
          </Tooltip>
        ),
      },
      {
        title: "作业票名称",
        dataIndex: "name",
        isShow: true,
        ellipsis: true,
        fixed: true,
        width: 48,
        render: (text, record) => (
          <Tooltip content={text}>
            <span
              className="block truncate text-blue-500 cursor-pointer"
              onClick={() => {
                handleOpenInfo(record);
              }}
            >
              {text}
            </span>
          </Tooltip>
        ),
      },
      ..._columns,
      ...(hiddenSettings?.specialClient === 2 ? columns2 : []),
      readonly
        ? {}
        : {
            title: "操作",
            isShow: true,
            dataIndex: "operate",
            key: "operate",
            align: "center",
            width: 40,
            fixed: "right",
            render: (text, record) => (
              <div>
                <ButtonGroup aria-label="操作按钮组">
                  {genBtn(
                    "view",
                    <Dropdown
                      trigger={"hover"}
                      position={"bottomLeft"}
                      render={
                        <Dropdown.Menu>
                          <Dropdown.Item
                            onClick={() => {
                              handleOpenInfo(record);
                            }}
                          >
                            查看
                          </Dropdown.Item>
                          {record?.canInspect ? (
                            <Dropdown.Item
                              onClick={() => {
                                handleOpenInspection(record);
                              }}
                            >
                              作业巡检
                            </Dropdown.Item>
                          ) : null}

                          <Dropdown.Item
                            onClick={() => {
                              handleOpenPrint(record);
                            }}
                          >
                            预览打印
                          </Dropdown.Item>
                        </Dropdown.Menu>
                      }
                    >
                      <Button>操作</Button>
                    </Dropdown>
                  )}
                  {genBtn("remove", <>{removeBtn(record)}</>)}
                </ButtonGroup>
              </div>
            ),
          },
    ];
  }, [_columns, mode, genBtn]);

  const handleOpenEdit = useCallback(
    (id?: string) => {
      setJsTemplateEdit({
        id: id ?? "",
        show: true,
      });
    },
    [setJsTemplateEdit]
  );

  const handlePageChange = useCallback(
    (currentPage: number) => {
      setjobSliceFilter({
        ...jobSliceFilter,
        pageNumber: currentPage,
      });
    },
    [jobSliceFilter, setjobSliceFilter]
  );

  const handlePageSizeChange = useCallback(
    (pageSize: number) => {
      setjobSliceFilter({
        ...jobSliceFilter,
        pageSize: pageSize,
      });
    },
    [jobSliceFilter, setjobSliceFilter]
  );

  const iframeId = `print-preview-${printId}`;

  const scollX = useMemo(() => {
    const sums: number[] = [];
    // 添加选择框列的宽度（如果不是只读模式且不是modal模式）
    if (mode !== "modal" && !readonly) {
      sums.push(TABLE_COLUMN_WIDTHS.SELECTION);
    }
    columns.forEach((o: any) => {
      sums.push(o?.width ?? 0);
    });
    return sum(sums);
  }, [columns, mode, readonly]);

  // 调试信息：检查选择框显示条件
  console.log("TicketListContent Debug:", {
    mode,
    readonly,
    shouldShowRowSelection: !(mode === "modal" || readonly),
    rowSelection: rowSelection,
  });

  return (
    <>
      <TableConfig
        visible={configModal}
        columns={_columns}
        handleClose={setShow}
        handleSave={setColumns}
      />
      <div className="bg-white shadow px-4 h-fit rounded overflow-hidden big_screen_table_box">
        {mode === "modal" ? null : (
          <div className="flex py-4 justify-between big_screen_table_filter_table_function big_screen_table_filter_operation">
            <div className="flex gap-4">
              {genBtn(
                "create",
                <button
                  className="btn rounded btn-primary btn-sm"
                  onClick={() => {
                    handleOpenEdit();
                  }}
                >
                  新增
                  <IconPlus size="small" />
                </button>
              )}
              {removesBtn()}
            </div>

            <div className="flex gap">
              <div className="tooltip" data-tip="刷新">
                <button
                  className="btn btn-sm btn-ghost rounded no-animation"
                  onClick={() => {
                    refetch();
                  }}
                >
                  <IconRefresh />
                </button>
              </div>
              <div className="tooltip" data-tip="下载">
                <button className="btn btn-sm btn-ghost rounded no-animation">
                  <IconDownload />
                </button>
              </div>
              {canExport ? (
                <div className="tooltip" data-tip="导出">
                  {genBtn(
                    "export",
                    <button
                      className="btn btn-sm btn-ghost rounded no-animation"
                      onClick={() => handleExport()}
                    >
                      <IconDownload />
                    </button>
                  )}
                </div>
              ) : null}
              <div className="tooltip" data-tip="打印">
                <button className="btn btn-sm btn-ghost rounded no-animation">
                  <IconPrint />
                </button>
              </div>

              <div className="tooltip" data-tip="设置">
                <button
                  className="btn btn-sm btn-ghost rounded no-animation"
                  onClick={handleOpenSetting}
                >
                  <IconSetting />
                </button>
              </div>
            </div>
          </div>
        )}

        <Table
          className="rounded  big_screen_table_filter_table"
          rowKey="id"
          columns={columns?.filter?.((o) => o?.fixed || o.isShow)}
          rowSelection={mode === "modal" || readonly ? false : rowSelection}
          dataSource={result}
          loading={isLoading}
          scroll={{
            x: scollX,
          }}
          headerStyle={{ color: "blue" }}
          bordered
          pagination={{
            showSizeChanger: true,
            popoverPosition: "topRight",
            currentPage: dataSource?.pageNumber ?? 1,
            pageSize: dataSource?.pageSize ?? 10,
            total: dataSource?.totalCount ?? 0,
            onPageChange: handlePageChange,
            onPageSizeChange: handlePageSizeChange,
            pageSizeOpts: [10, 15, 20, 50],
          }}
        />
      </div>
      {inspection.show ? <InspectionModal /> : null}

      <Modal
        centered
        title="打印预览"
        footer={
          <>
            <button
              className="btn btn-sm btn-ghost mr-3"
              onClick={handleCancelPrint}
            >
              取消
            </button>
            <button
              className="btn btn-sm btn-primary"
              onClick={() => {
                document.getElementById(iframeId).contentWindow.window.print();
              }}
            >
              打印
            </button>
          </>
        }
        visible={!!printId}
        onCancel={handleCancelPrint}
        style={{
          width: 848,
          height: 1282,
        }}
      >
        <iframe
          id={iframeId}
          className="w-full h-full"
          src={`/print_preview/${printId}`}
        />
      </Modal>
    </>
  );
}
