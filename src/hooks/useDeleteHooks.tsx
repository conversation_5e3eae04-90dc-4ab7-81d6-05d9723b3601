import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>rm, Toast } from "@douyinfe/semi-ui";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useCallback, useMemo, useState } from "react";

type DeleteHooksProps = {
  deleteFn: any;
  deletesFn?: any;
  queryKey: string;
};

type RowSelectionType = {
  fixed: boolean;
  width: number;
  onChange: (selectedRowKeys: any) => void;
};

export const useDeleteHooks = ({
  deleteFn,
  deletesFn,
  queryKey,
}: DeleteHooksProps): [
  (record: any) => JSX.Element,
  RowSelectionType,
  () => JSX.Element,
] => {
  const queryClient = useQueryClient();
  const [rows, setRows] = useState<number[]>([]);
  const mutation = useMutation({
    mutationFn: deleteFn,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      let opts = {
        content: `操作成功!`,
        duration: 2,
      };
      queryClient.invalidateQueries({ queryKey: [queryKey] });
      Toast.success(opts);
    },
  });

  const removes = useMutation({
    mutationFn: deletesFn,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      let opts = {
        content: `操作成功!`,
        duration: 2,
      };
      queryClient.invalidateQueries({ queryKey: [queryKey] });
      Toast.success(opts);
    },
  });

  const rowSelection = useMemo(
    () => ({
      fixed: true,
      width: 32, // 设置选择框列宽度为32px，适合复选框大小
      onChange: (selectedRowKeys) => {
        setRows(selectedRowKeys);
      },
    }),
    [setRows]
  );

  const handleConfirm = useCallback(
    (record: any) => {
      mutation.mutate(record.id);
    },
    [mutation]
  );

  const handleRemoves = useCallback(() => {
    removes.mutate(rows);
    setRows([]);
  }, [removes, rows, setRows]);

  const removeBtn = (record: any) => {
    return (
      <Popconfirm
        position="bottomRight"
        title="确定是否要删除该项？"
        content="此修改将不可逆"
        okType="danger"
        okButtonProps={{
          className: "semi-button semi-button-danger semi-button-light",
        }}
        onConfirm={() => {
          handleConfirm(record);
        }}
      >
        <Button type="danger">删除</Button>
      </Popconfirm>
    );
  };
  const removesBtn = () => {
    return (
      <>
        <span className="flex text-gray-900 text-opacity-60 text-sm justify-center items-center big_screen_table_filter_table_select_text">
          已选中{rows?.length ?? 0}个
        </span>
        {rows?.length ? (
          <Popconfirm
            title="确定是否要删除该项？"
            content="此修改将不可逆"
            okType="danger"
            okButtonProps={{
              className: "semi-button semi-button-danger semi-button-light",
            }}
            onConfirm={handleRemoves}
          >
            <button className="btn btn-sm rounded">批量删除</button>
          </Popconfirm>
        ) : null}
      </>
    );
  };

  return [removeBtn, rowSelection, removesBtn];
};
