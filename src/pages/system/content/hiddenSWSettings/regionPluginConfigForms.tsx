import { Card, Col, Divider, Form, Row, useFormState } from "@douyinfe/semi-ui";
import { OPEN_NOTOPEN_MAP } from "components";
import { motion } from "framer-motion";

// 浙江表单内容组件
// 江西平台表单内容组件
export const JiangxiConfigContent = ({ readonly = false, ...restProps }) => {
  const formState = useFormState();

  const gutter = 24;
  const rules = [{ required: true, message: "此为必填项" }];

  if (formState?.values.reportType !== 8) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">配置修改</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置基本信息和连接参数</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="企业名编码"
              field="companyCode"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="验证令牌"
              field="authorizationCode"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={24}>
            <Form.Input
              label="上报接入地址"
              field="urlPrefix"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
      </Card>

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#3B82F6]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">上报开关设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置各项上报功能的开关</span>
        </div>
        {[
          [
            { label: "作业票上报是否打开", field: "ticketIsOn" },
            { label: "报备上报是否打开", field: "ticketBackupIsOn" },
          ],
          [
            { label: "截屏上报是否打开", field: "ticketScreenshotIsOn" },
            { label: "视频上报是否打开", field: "ticketVideoIsOn" },
          ],
        ].map((rowItems) => (
          <Row key={rowItems[0].field} gutter={gutter}>
            {rowItems.map(({ label, field }) => (
              <Col key={field} span={12}>
                <Form.RadioGroup label={label} field={field} rules={rules}>
                  {OPEN_NOTOPEN_MAP.map((item) => (
                    <Form.Radio key={item.id} value={item.id}>
                      {item.name}
                    </Form.Radio>
                  ))}
                </Form.RadioGroup>
              </Col>
            ))}
          </Row>
        ))}
      </Card>

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#8B5CF6]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">其他设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置超时和文件大小限制</span>
        </div>
        <Row gutter={gutter}>
          <Col span={8}>
            <Form.InputNumber
              label="截图作业超时时间(秒)"
              field="screenshotTimeout"
              min={1}
              rules={rules}
            />
          </Col>
          <Col span={8}>
            <Form.InputNumber
              label="截图文件最小文件大小(KB)"
              field="screenshotMinSize"
              min={1}
              rules={rules}
            />
          </Col>
          <Col span={8}>
            <Form.InputNumber
              label="请求超时(秒)"
              field="queryTimeout"
              min={1}
              rules={rules}
            />
          </Col>
        </Row>
      </Card>
    </motion.div>
  );
};

export const ZhejiangConfigContent = ({ readonly = false, ...restProps }) => {
  const formState = useFormState();

  const gutter = 24;
  const rules = [{ required: true, message: "此为必填项" }];

  if (formState?.values.reportType !== 7) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">上报基础设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置基本信息和连接参数</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="企业编码"
              field="companyCode"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="应用key变量"
              field="appKey"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="应用secret变量"
              field="appSecret"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="加密算法key变量"
              field="encryptKey"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="加密算法iv变量"
              field="encryptIv"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="上报接入地址"
              field="urlPrefix"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
      </Card>

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#3B82F6]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">上报开关设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置各项上报功能的开关</span>
        </div>
        {[
          [
            { label: "作业票上报是否打开", field: "ticketIsOn" },
            { label: "报备上报是否打开", field: "ticketBackupIsOn" },
            { label: "截屏上报是否打开", field: "ticketScreenshotIsOn" },
          ],
          [
            { label: "视频上报是否打开", field: "ticketVideoIsOn" },
            { label: "基础上报是否打开", field: "ticketCommonIsOn" },
            { label: "报警上报是否打开", field: "ticketAlarmIsOn" },
          ],
        ].map((rowItems) => (
          <Row key={rowItems[0].field} gutter={gutter}>
            {rowItems.map(({ label, field }) => (
              <Col key={field} span={8}>
                <Form.RadioGroup label={label} field={field} rules={rules}>
                  {OPEN_NOTOPEN_MAP.map((item) => (
                    <Form.Radio key={item.id} value={item.id}>
                      {item.name}
                    </Form.Radio>
                  ))}
                </Form.RadioGroup>
              </Col>
            ))}
          </Row>
        ))}
      </Card>

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#8B5CF6]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">其他设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置超时和文件大小限制</span>
        </div>
        <Row gutter={gutter}>
          <Col span={8}>
            <Form.InputNumber
              label="截图作业超时时间(秒)"
              field="screenshotTimeout"
              min={1}
              rules={rules}
            />
          </Col>
          <Col span={8}>
            <Form.InputNumber
              label="截图文件最小文件大小(KB)"
              field="screenshotMinSize"
              min={1}
              rules={rules}
            />
          </Col>
          <Col span={8}>
            <Form.InputNumber
              label="请求超时(秒)"
              field="queryTimeout"
              min={1}
              rules={rules}
            />
          </Col>
        </Row>
      </Card>
    </motion.div>
  );
};

// 优化的云南表单内容组件
export const YunNanConfigContent = ({ readonly = false, ...restProps }) => {
  const formState = useFormState();

  const gutter = 24;
  const rules = [{ required: true, message: "此为必填项" }];

  if (formState?.values.reportType !== 1) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">上报基础设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置基本信息和连接参数</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input label="企业编码" field="companyCode" trigger="blur" />
          </Col>
          <Col span={12}>
            <Form.Input
              label="加密算法key变量"
              field="encryptKey"
              trigger="blur"
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="加密算法iv变量"
              field="encryptIv"
              trigger="blur"
            />
          </Col>
          <Col span={12}>
            <Form.Input label="用户名" field="username" trigger="blur" />
          </Col>
          <Col span={12}>
            <Form.Input label="密码" field="password" trigger="blur" />
          </Col>
          <Col span={12}>
            <Form.Input label="上报接入地址" field="urlPrefix" trigger="blur" />
          </Col>
        </Row>
      </Card>

      {/* <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">上报接口设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置上报接口地址</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="作业票上报接口"
              field="ticketUri"
              trigger="blur"
            />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="作业票表单文件上报接口"
              field="ticketFileUri"
              trigger="blur"
            />
          </Col>
        </Row>
      </Card> */}

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#0072E5]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">上报开关设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">控制上报功能开关</span>
        </div>
        {[
          [
            { label: "作业票上报是否打开", field: "ticketIsOn" },
            { label: "报备上报是否打开", field: "ticketBackupIsOn" },
          ],
          [
            { label: "截屏上报是否打开", field: "ticketScreenshotIsOn" },
            { label: "视频上报是否打开", field: "ticketVideoIsOn" },
          ],
        ].map((rowItems) => (
          <Row key={rowItems[0].field} gutter={gutter}>
            {rowItems.map(({ label, field }) => (
              <Col key={field} span={12}>
                <Form.RadioGroup label={label} field={field} rules={rules}>
                  {OPEN_NOTOPEN_MAP.map((item) => (
                    <Form.Radio key={item.id} value={item.id}>
                      {item.name}
                    </Form.Radio>
                  ))}
                </Form.RadioGroup>
              </Col>
            ))}
          </Row>
        ))}
      </Card>

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">其他设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置截图相关参数</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.InputNumber
              label="截图操作超时时间(秒)"
              field="screenshotTimeout"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.InputNumber
              label="截图文件最小文件大小"
              field="screenshotMinSize"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={8}>
            <Form.InputNumber
              label="请求超时(秒)"
              field="queryTimeout"
              min={1}
              rules={rules}
            />
          </Col>
        </Row>
      </Card>
    </motion.div>
  );
};

// 优化的江西表单内容组件
export const JiangxiXinganyanConfigContent = ({
  readonly = false,
  ...restProps
}) => {
  const formState = useFormState();

  const gutter = 24;
  const rules = [{ required: true, message: "此为必填项" }];

  if (formState?.values.reportType !== 2) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#0072E5]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">上报基础设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置基本信息和连接参数</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input label="企业编码" field="companyCode" trigger="blur" />
          </Col>
          <Col span={12}>
            <Form.Input label="上报接入地址" field="urlPrefix" trigger="blur" />
          </Col>
        </Row>
      </Card>

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#0072E5]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">上报开关设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">控制上报功能开关</span>
        </div>
        {[
          [
            { label: "作业票上报是否打开", field: "ticketIsOn" },
            { label: "报备上报是否打开", field: "ticketBackupIsOn" },
          ],
          [
            { label: "流程上报是否打开", field: "ticketProgressIsOn" },
            { label: "截屏上报是否打开", field: "ticketScreenshotIsOn" },
          ],
          [
            { label: "视频上报是否打开", field: "ticketVideoIsOn" },
            { label: "作业人员上报是否打开", field: "ticketPersonIsOn" },
          ],
          [{ label: "活动上报是否打开", field: "ticketActivityIsOn" }],
        ].map((rowItems) => (
          <Row key={rowItems[0].field} gutter={gutter}>
            {rowItems.map(({ label, field }) => (
              <Col key={field} span={12}>
                <Form.RadioGroup label={label} field={field} rules={rules}>
                  {OPEN_NOTOPEN_MAP.map((item) => (
                    <Form.Radio key={item.id} value={item.id}>
                      {item.name}
                    </Form.Radio>
                  ))}
                </Form.RadioGroup>
              </Col>
            ))}
          </Row>
        ))}
      </Card>
      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">其他设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置截图相关参数</span>
        </div>
        <Row gutter={gutter}>
          <Col span={8}>
            <Form.InputNumber
              label="截图操作超时时间(秒)"
              field="screenshotTimeout"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={8}>
            <Form.InputNumber
              label="截图文件最小文件大小"
              field="screenshotMinSize"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={8}>
            <Form.InputNumber
              label="请求超时(秒)"
              field="queryTimeout"
              min={1}
              rules={rules}
            />
          </Col>
        </Row>
      </Card>
    </motion.div>
  );
};

// 优化的江西-万年凤巢工业区表单内容组件
export const JiangxiWannianFengchaoConfigContent = ({
  readonly = false,
  ...restProps
}) => {
  const formState = useFormState();

  const gutter = 24;
  const rules = [{ required: true, message: "此为必填项" }];

  if (formState?.values.reportType !== 3) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#0072E5]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">上报基础设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置基本信息和连接参数</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input label="企业编码" field="companyCode" trigger="blur" />
          </Col>
          <Col span={12}>
            <Form.Input label="上报接入地址" field="urlPrefix" trigger="blur" />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="企业名称"
              field="companyName"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="企业信用代码"
              field="socialCreditCode"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
      </Card>

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#0072E5]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">上报开关设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">控制上报功能开关</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.RadioGroup
              label="作业票上报是否打开"
              field="ticketIsOn"
              rules={[{ required: true, message: "此为必填项" }]}
            >
              {OPEN_NOTOPEN_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
          <Col span={12}>
            <Form.RadioGroup
              label="报备上报是否打开"
              field="ticketBackupIsOn"
              rules={[{ required: true, message: "此为必填项" }]}
            >
              {OPEN_NOTOPEN_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.RadioGroup
              label="流程上报是否打开"
              field="ticketProgressIsOn"
              rules={[{ required: true, message: "此为必填项" }]}
            >
              {OPEN_NOTOPEN_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
          <Col span={12}>
            <Form.RadioGroup
              label="截屏上报是否打开"
              field="ticketScreenshotIsOn"
              rules={[{ required: true, message: "此为必填项" }]}
            >
              {OPEN_NOTOPEN_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
        </Row>
      </Card>

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">其他设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置截图相关参数</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.InputNumber
              label="截图操作超时时间(秒)"
              field="screenshotTimeout"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.InputNumber
              label="截图文件最小文件大小"
              field="screenshotMinSize"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
      </Card>
    </motion.div>
  );
};

// 优化的辽宁-盘锦表单内容组件
export const LiaoningPanjinConfigContent = ({
  readonly = false,
  ...restProps
}) => {
  const formState = useFormState();

  const gutter = 24;
  const rules = [{ required: true, message: "此为必填项" }];

  if (formState?.values.reportType !== 4) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      {/* 上报基础设置 */}
      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#1E88E5]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">上报基础设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置基本信息和连接参数</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="企业编码"
              field="companyCode"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="企业信用代码"
              field="socialCreditCode"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="上报接入地址"
              field="urlPrefix"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="验证令牌"
              field="authorizationCode"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
      </Card>

      {/* 上报开关设置 */}
      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#1E88E5]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">上报开关设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">控制上报功能开关</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.RadioGroup
              label="公共信息上报是否打开"
              field="ticketCommonIsOn"
              rules={rules}
            >
              {OPEN_NOTOPEN_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
          <Col span={12}>
            <Form.RadioGroup
              label="作业票上报是否打开"
              field="ticketIsOn"
              rules={rules}
            >
              {OPEN_NOTOPEN_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.RadioGroup
              label="截屏上报是否打开"
              field="ticketScreenshotIsOn"
              rules={rules}
            >
              {OPEN_NOTOPEN_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
          <Col span={12}>
            <Form.RadioGroup
              label="流程上报是否打开"
              field="ticketProgressIsOn"
              rules={rules}
            >
              {OPEN_NOTOPEN_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.RadioGroup
              label="安全措施上报是否打开"
              field="ticketMeasureIsOn"
              rules={rules}
            >
              {OPEN_NOTOPEN_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>

          <Col span={12}>
            <Form.RadioGroup
              label="气体分析上报是否打开"
              field="ticketGasIsOn"
              rules={rules}
            >
              {OPEN_NOTOPEN_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.RadioGroup
              label="视频上报是否打开"
              field="ticketVideoIsOn"
              rules={rules}
            >
              {OPEN_NOTOPEN_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
          <Col span={12}>
            <Form.RadioGroup
              label="关联作业票上报是否打开"
              field="ticketRelateIsOn"
              rules={rules}
            >
              {OPEN_NOTOPEN_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
        </Row>
      </Card>

      {/* 其他设置 */}
      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">其他设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置截图相关参数</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.InputNumber
              label="截图操作超时时间(秒)"
              field="screenshotTimeout"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.InputNumber
              label="截图文件最小文件大小"
              field="screenshotMinSize"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
      </Card>
    </motion.div>
  );
};

// 优化的江西-乐平工业园表单内容组件
export const JiangxiLepingConfigContent = ({
  readonly = false,
  ...restProps
}) => {
  const formState = useFormState();

  const gutter = 24;
  const rules = [{ required: true, message: "此为必填项" }];

  if (formState?.values.reportType !== 6) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      {/* 上报基础设置 */}
      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#0072E5]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">上报基础设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置基本信息和连接参数</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="企业编码"
              field="companyCode"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="企业名称"
              field="companyName"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="企业社会信用代码"
              field="socialCreditCode"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="用户名"
              field="username"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="密码"
              field="password"
              // type="password"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="加密算法key变量"
              field="encryptKey"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="加密算法iv变量"
              field="encryptIv"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="上报接入地址"
              field="urlPrefix"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
      </Card>

      {/* 上报开关设置 */}
      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#0072E5]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">上报开关设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">控制上报功能开关</span>
        </div>
        {[
          [
            { label: "作业票上报是否打开", field: "ticketIsOn" },
            { label: "报备上报是否打开", field: "ticketBackupIsOn" },
          ],
          [
            { label: "截屏上报是否打开", field: "ticketScreenshotIsOn" },
          ],
        ].map((rowItems) => (
          <Row key={rowItems[0].field} gutter={gutter}>
            {rowItems.map(({ label, field }) => (
              <Col key={field} span={12}>
                <Form.RadioGroup label={label} field={field} rules={rules}>
                  {OPEN_NOTOPEN_MAP.map((item) => (
                    <Form.Radio key={item.id} value={item.id}>
                      {item.name}
                    </Form.Radio>
                  ))}
                </Form.RadioGroup>
              </Col>
            ))}
          </Row>
        ))}
      </Card>

      {/* 其他设置 */}
      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">其他设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置截图和网络相关参数</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.InputNumber
              label="截图操作超时时间(秒)"
              field="screenshotTimeout"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.InputNumber
              label="截图文件最小文件大小"
              field="screenshotMinSize"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.InputNumber
              label="批处理最大记录数"
              field="batchNum"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.InputNumber
              label="请求超时"
              field="queryTimeout"
              trigger="blur"
              suffix="秒"
              rules={rules}
            />
          </Col>
        </Row>
      </Card>
    </motion.div>
  );
};

// 智园园区平台表单内容组件
export const ZhiYuanConfigContent = ({ readonly = false, ...restProps }) => {
  const formState = useFormState();
  const gutter = 24;
  const rules = [{ required: true, message: "此为必填项" }];

  if (formState?.values.reportType !== 9) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">上报基础配置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置基本信息和连接参数</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="企业名编码"
              field="companyCode"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="加密算法key变量"
              field="encryptKey"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="加密算法iv变量"
              field="encryptIv"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="用户名"
              field="username"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="密码"
              field="password"
              type="password"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="上报接入地址"
              field="urlPrefix"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
      </Card>

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#3B82F6]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">上报开关设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置各项上报功能的开关</span>
        </div>
        {[
          [
            { label: "作业票上报是否打开", field: "ticketIsOn" },
            { label: "报备上报是否打开", field: "ticketBackupIsOn" },
          ],
          [
            { label: "截屏上报是否打开", field: "ticketScreenshotIsOn" },
            { label: "视频上报是否打开", field: "ticketVideoIsOn" },
          ],
          [
            { label: "作业流程上报是否打开", field: "ticketProgressIsOn" },
            { label: "安全措施分析上报是否打开", field: "ticketMeasureIsOn" },
          ],
          [{ label: "气体检测上报是否打开", field: "ticketGasIsOn" }],
        ].map((rowItems) => (
          <Row key={rowItems[0].field} gutter={gutter}>
            {rowItems.map(({ label, field }) => (
              <Col key={field} span={12}>
                <Form.RadioGroup label={label} field={field} rules={rules}>
                  {OPEN_NOTOPEN_MAP.map((item) => (
                    <Form.Radio key={item.id} value={item.id}>
                      {item.name}
                    </Form.Radio>
                  ))}
                </Form.RadioGroup>
              </Col>
            ))}
          </Row>
        ))}
      </Card>

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#8B5CF6]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">其他设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置超时和文件大小限制</span>
        </div>
        <Row gutter={gutter}>
          <Col span={8}>
            <Form.InputNumber
              label="截图作业超时时间(秒)"
              field="screenshotTimeout"
              min={1}
              rules={rules}
            />
          </Col>
          <Col span={8}>
            <Form.InputNumber
              label="截图文件最小文件大小(KB)"
              field="screenshotMinSize"
              min={1}
              rules={rules}
            />
          </Col>
          <Col span={8}>
            <Form.InputNumber
              label="请求超时(秒)"
              field="queryTimeout"
              min={1}
              rules={rules}
            />
          </Col>
        </Row>
      </Card>
    </motion.div>
  );
};

export const HebeiConfigContent = ({ readonly = false, ...restProps }) => {
  const formState = useFormState();

  const gutter = 24;
  const rules = [{ required: true, message: "此为必填项" }];

  if (formState?.values.reportType !== 5) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      {/* 上报基础设置 */}
      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#1E88E5]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">上报基础设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置基本信息和连接参数</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input label="企业编码" field="companyCode" trigger="blur" />
          </Col>
          <Col span={12}>
            <Form.Input
              label="加密算法key变量"
              field="encryptKey"
              trigger="blur"
            />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="加密算法iv变量"
              field="encryptIv"
              trigger="blur"
            />
          </Col>
          <Col span={12}>
            <Form.Input label="上报接入地址" field="urlPrefix" trigger="blur" />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="验证令牌"
              field="authorizationCode"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="上报查询接入地址"
              field="queryUrlPrefix"
              trigger="blur"
            />
          </Col>
        </Row>
      </Card>

      {/* 上报开关设置 */}
      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#1E88E5]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">上报开关设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">控制上报功能开关</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.RadioGroup
              label="作业票上报是否打开"
              field="ticketIsOn"
              rules={rules}
            >
              {OPEN_NOTOPEN_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
          <Col span={12}>
            <Form.RadioGroup
              label="作业报备上报是否打开"
              field="ticketBackupIsOn"
              rules={[{ required: true, message: "此为必填项" }]}
            >
              {OPEN_NOTOPEN_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.RadioGroup
              label="截屏上报是否打开"
              field="ticketScreenshotIsOn"
              rules={rules}
            >
              {OPEN_NOTOPEN_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
          <Col span={12}>
            <Form.RadioGroup
              label="视频上报是否打开"
              field="ticketVideoIsOn"
              rules={rules}
            >
              {OPEN_NOTOPEN_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
        </Row>
      </Card>

      {/* 其他设置 */}
      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">其他设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置截图相关参数</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.InputNumber
              label="截图操作超时时间(秒)"
              field="screenshotTimeout"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.InputNumber
              label="截图文件最小文件大小"
              field="screenshotMinSize"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.InputNumber
              label="批处理最大记录数"
              field="batchNum"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.InputNumber
              label="批处理上报后查询间隔"
              field="queryBatchInterval"
              trigger="blur"
              suffix="秒"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.InputNumber
              label="请求超时"
              field="queryTimeout"
              trigger="blur"
              suffix="秒"
              rules={rules}
            />
          </Col>
        </Row>
      </Card>
    </motion.div>
  );
};

// 浙江宁波园区平台表单内容组件
export const ZhejiangNingboConfigContent = ({
  readonly = false,
  ...restProps
}) => {
  const formState = useFormState();
  const gutter = 24;
  const rules = [{ required: true, message: "此为必填项" }];

  if (formState?.values.reportType !== 10) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">上报基础配置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置基本信息和连接参数</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="企业名编码"
              field="companyCode"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="应用key变量"
              field="appKey"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="应用secret变量"
              field="appSecret"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="上报接入地址"
              field="urlPrefix"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
      </Card>

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#3B82F6]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">上报开关设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置各项上报功能的开关</span>
        </div>
        {[
          [
            { label: "作业票上报是否打开", field: "ticketIsOn" },
            { label: "报备上报是否打开", field: "ticketBackupIsOn" },
          ],
          [
            { label: "截屏上报是否打开", field: "ticketScreenshotIsOn" },
            { label: "视频上报是否打开", field: "ticketVideoIsOn" },
          ],
          [
            { label: "作业流程上报是否打开", field: "ticketProgressIsOn" },
            { label: "安全措施分析上报是否打开", field: "ticketMeasureIsOn" },
          ],
          [
            { label: "气体检测上报是否打开", field: "ticketGasIsOn" },
            { label: "作业人员上报是否打开", field: "ticketPersonIsOn" },
          ],
        ].map((rowItems) => (
          <Row key={rowItems[0].field} gutter={gutter}>
            {rowItems.map(({ label, field }) => (
              <Col key={field} span={12}>
                <Form.RadioGroup label={label} field={field} rules={rules}>
                  {OPEN_NOTOPEN_MAP.map((item) => (
                    <Form.Radio key={item.id} value={item.id}>
                      {item.name}
                    </Form.Radio>
                  ))}
                </Form.RadioGroup>
              </Col>
            ))}
          </Row>
        ))}
      </Card>

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#8B5CF6]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">其他设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置超时和文件大小限制</span>
        </div>
        <Row gutter={gutter}>
          <Col span={8}>
            <Form.InputNumber
              label="截图作业超时时间(秒)"
              field="screenshotTimeout"
              min={1}
              rules={rules}
            />
          </Col>
          <Col span={8}>
            <Form.InputNumber
              label="截图文件最小文件大小(KB)"
              field="screenshotMinSize"
              min={1}
              rules={rules}
            />
          </Col>
          <Col span={8}>
            <Form.InputNumber
              label="请求超时(秒)"
              field="queryTimeout"
              min={1}
              rules={rules}
            />
          </Col>
        </Row>
      </Card>
    </motion.div>
  );
};
