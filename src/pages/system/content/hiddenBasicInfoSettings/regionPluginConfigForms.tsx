import { Card, Col, Divider, Form, Row, useFormState } from "@douyinfe/semi-ui";
import { OPEN_NOTOPEN_MAP } from "components";
import { motion } from "framer-motion";

// 江西新干盐化城表单内容组件
export const JiangxiXinganConfigContent = ({
  readonly = false,
  ...restProps
}) => {
  const formState = useFormState();
  const gutter = 24;
  const rules = [{ required: true, message: "此为必填项" }];

  if (formState?.values.reportType !== 3) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">基础配置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置基本信息和连接参数</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="企业编码"
              field="companyCode"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="上报接入地址"
              field="urlPrefix"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
      </Card>

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#3B82F6]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">开关配置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置各项上报功能的开关</span>
        </div>
        {[[{ label: "员工上报是否打开", field: "employeeIsOn" }]].map(
          (rowItems, rowIndex) => (
            <Row
              key={rowIndex}
              gutter={gutter}
              className={rowIndex > 0 ? "mt-4" : ""}
            >
              {rowItems.map(({ label, field }) => (
                <Col key={field} span={12}>
                  <Form.RadioGroup label={label} field={field} rules={rules}>
                    {OPEN_NOTOPEN_MAP.map((item) => (
                      <Form.Radio key={item.id} value={item.id}>
                        {item.name}
                      </Form.Radio>
                    ))}
                  </Form.RadioGroup>
                </Col>
              ))}
            </Row>
          )
        )}
      </Card>

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#8B5CF6]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">其他设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置超时设置</span>
        </div>
        <Row gutter={gutter}>
          <Col span={8}>
            <Form.InputNumber
              label="请求超时(秒)"
              field="queryTimeout"
              min={1}
              rules={rules}
            />
          </Col>
        </Row>
      </Card>
    </motion.div>
  );
};

// 智园园区表单内容组件
export const ZhiyuanConfigContent = ({ readonly = false, ...restProps }) => {
  const formState = useFormState();
  const gutter = 24;
  const rules = [{ required: true, message: "此为必填项" }];

  if (formState?.values.reportType !== 5) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">上报基础配置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置基本信息和连接参数</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="企业名编码"
              field="companyCode"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="企业名称"
              field="companyName"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="企业信用代码"
              field="socialCreditCode"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="加密算法key变量"
              field="encryptKey"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="加密算法iv变量"
              field="encryptIv"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="用户名"
              field="username"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="密码"
              field="password"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="上报接入地址"
              field="urlPrefix"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
      </Card>

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#3B82F6]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">开关配置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置各项上报功能的开关</span>
        </div>
        {[
          [
            { label: "员工上报", field: "employeeIsOn" },
            { label: "承包商员工上报", field: "contractorEmployeeIsOn" },
          ],
          [
            { label: "设备停用上报", field: "equipmentStopIsOn" },
            { label: "三同时上报", field: "threeSyncIsOn" },
          ],
          [
            { label: "装置开停车上报", field: "plantSsIsOn" },
            { label: "装置大修上报", field: "plantMaintenanceIsOn" },
          ],
        ].map((rowItems, rowIndex) => (
          <Row
            key={rowIndex}
            gutter={gutter}
            className={rowIndex > 0 ? "mt-4" : ""}
          >
            {rowItems.map(({ label, field }) => (
              <Col key={field} span={12}>
                <Form.RadioGroup label={label} field={field} rules={rules}>
                  {OPEN_NOTOPEN_MAP.map((item) => (
                    <Form.Radio key={item.id} value={item.id}>
                      {item.name}
                    </Form.Radio>
                  ))}
                </Form.RadioGroup>
              </Col>
            ))}
          </Row>
        ))}
      </Card>

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#8B5CF6]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">其他配置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置超时设置</span>
        </div>
        <Row gutter={gutter}>
          <Col span={8}>
            <Form.InputNumber
              label="请求超时(秒)"
              field="queryTimeout"
              min={1}
              rules={rules}
            />
          </Col>
        </Row>
      </Card>
    </motion.div>
  );
};

// 浙江宁波园区表单内容组件
export const ZhejiangNingboConfigContent = ({
  readonly = false,
  ...restProps
}) => {
  const formState = useFormState();
  const gutter = 24;
  const rules = [{ required: true, message: "此为必填项" }];

  if (formState?.values.reportType !== 4) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">上报基础配置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置基本信息和连接参数</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="企业名编码"
              field="companyCode"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="应用key变量"
              field="appKey"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="应用secret变量"
              field="appSecret"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="上报接入地址"
              field="urlPrefix"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
      </Card>

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#3B82F6]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">开关配置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置各项上报功能的开关</span>
        </div>
        {[
          [
            { label: "设备停用上报", field: "equipmentStopIsOn" },
            { label: "三同时上报", field: "threeSyncIsOn" },
          ],
          [
            { label: "装置开停车上报", field: "plantSsIsOn" },
            { label: "装置大修上报", field: "plantMaintenanceIsOn" },
          ],
        ].map((rowItems, rowIndex) => (
          <Row
            key={rowIndex}
            gutter={gutter}
            className={rowIndex > 0 ? "mt-4" : ""}
          >
            {rowItems.map(({ label, field }) => (
              <Col key={field} span={12}>
                <Form.RadioGroup label={label} field={field} rules={rules}>
                  {OPEN_NOTOPEN_MAP.map((item) => (
                    <Form.Radio key={item.id} value={item.id}>
                      {item.name}
                    </Form.Radio>
                  ))}
                </Form.RadioGroup>
              </Col>
            ))}
          </Row>
        ))}
      </Card>

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#8B5CF6]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">其他配置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置超时和批处理参数</span>
        </div>
        <Row gutter={gutter}>
          <Col span={8}>
            <Form.InputNumber
              label="请求超时(秒)"
              field="queryTimeout"
              min={1}
              rules={rules}
            />
          </Col>
          <Col span={8}>
            <Form.InputNumber
              label="批处理请求个数"
              field="batchNum"
              min={1}
              rules={rules}
            />
          </Col>
          <Col span={8}>
            <Form.InputNumber
              label="批处理查询间隔(秒)"
              field="batchQueryInterval"
              min={1}
              rules={rules}
            />
          </Col>
        </Row>
      </Card>
    </motion.div>
  );
};

// 江西平台表单内容组件
export const JiangxiConfigContent = ({ readonly = false, ...restProps }) => {
  const formState = useFormState();

  const gutter = 24;
  const rules = [{ required: true, message: "此为必填项" }];

  if (formState?.values.reportType !== 2) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">基础配置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置基本信息和连接参数</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="企业名编码"
              field="companyCode"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="验证令牌"
              field="authorizationCode"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={24}>
            <Form.Input
              label="上报接入地址"
              field="urlPrefix"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
      </Card>

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#3B82F6]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">开关配置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置各项上报功能的开关</span>
        </div>
        {[
          [
            { label: "员工上报是否打开", field: "employeeIsOn" },
            { label: "设备停用上报", field: "equipmentStopIsOn" },
          ],
          [
            { label: "三同时上报", field: "threeSyncIsOn" },
            { label: "装置开停车上报", field: "plantSsIsOn" },
          ],
          [{ label: "装置大修上报", field: "plantMaintenanceIsOn" }],
        ].map((rowItems, rowIndex) => (
          <Row
            key={rowIndex}
            gutter={gutter}
            className={rowIndex > 0 ? "mt-4" : ""}
          >
            {rowItems.map(({ label, field }) => (
              <Col key={field} span={12}>
                <Form.RadioGroup label={label} field={field} rules={rules}>
                  {OPEN_NOTOPEN_MAP.map((item) => (
                    <Form.Radio key={item.id} value={item.id}>
                      {item.name}
                    </Form.Radio>
                  ))}
                </Form.RadioGroup>
              </Col>
            ))}
          </Row>
        ))}
      </Card>

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#8B5CF6]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">其他设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置超时和文件大小限制</span>
        </div>
        <Row gutter={gutter}>
          <Col span={8}>
            <Form.InputNumber
              label="批处理请求个数"
              field="batchNum"
              min={1}
              rules={rules}
            />
          </Col>
          <Col span={8}>
            <Form.InputNumber
              label="批处理查询间隔(秒)"
              field="batchQueryInterval"
              min={1}
              rules={rules}
            />
          </Col>
          <Col span={8}>
            <Form.InputNumber
              label="请求超时(秒)"
              field="queryTimeout"
              min={1}
              rules={rules}
            />
          </Col>
        </Row>
      </Card>
    </motion.div>
  );
};

// 浙江表单内容组件
export const ZhejiangConfigContent = ({ readonly = false, ...restProps }) => {
  const formState = useFormState();

  const gutter = 24;
  const rules = [{ required: true, message: "此为必填项" }];

  if (formState?.values.reportType !== 1) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">基础配置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置基本信息和连接参数</span>
        </div>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="企业名编码"
              field="companyCode"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="应用key变量"
              field="appKey"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="应用secret变量"
              field="appSecret"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="加密算法key变量"
              field="encryptKey"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Input
              label="加密算法iv变量"
              field="encryptIv"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="上报接入地址"
              field="urlPrefix"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
      </Card>

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#3B82F6]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">开关配置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置各项上报功能的开关</span>
        </div>
        {[
          [
            { label: "设备停用上报", field: "equipmentStopIsOn" },
            { label: "三同时上报", field: "threeSyncIsOn" },
          ],
          [
            { label: "装置开停车上报", field: "plantSsIsOn" },
            { label: "装置大修上报", field: "plantMaintenanceIsOn" },
          ],
        ].map((rowItems, rowIndex) => (
          <Row
            key={rowIndex}
            gutter={gutter}
            className={rowIndex > 0 ? "mt-4" : ""}
          >
            {rowItems.map(({ label, field }) => (
              <Col key={field} span={12}>
                <Form.RadioGroup label={label} field={field} rules={rules}>
                  {OPEN_NOTOPEN_MAP.map((item) => (
                    <Form.Radio key={item.id} value={item.id}>
                      {item.name}
                    </Form.Radio>
                  ))}
                </Form.RadioGroup>
              </Col>
            ))}
          </Row>
        ))}
      </Card>

      <Card className="hover:shadow-md transition-all border-l-4 border-l-[#8B5CF6]">
        <div className="flex items-center mb-4">
          <span className="text-lg font-medium">其他设置</span>
          <Divider layout="vertical" margin="12px" />
          <span className="text-xs text-gray-500">配置超时和文件大小限制</span>
        </div>
        <Row gutter={gutter}>
          <Col span={8}>
            <Form.InputNumber
              label="批处理请求个数"
              field="batchNum"
              min={1}
              rules={rules}
            />
          </Col>
          <Col span={8}>
            <Form.InputNumber
              label="批处理查询间隔(秒)"
              field="batchQueryInterval"
              min={1}
              rules={rules}
            />
          </Col>
          <Col span={8}>
            <Form.InputNumber
              label="请求超时(秒)"
              field="queryTimeout"
              min={1}
              rules={rules}
            />
          </Col>
        </Row>
      </Card>
    </motion.div>
  );
};
