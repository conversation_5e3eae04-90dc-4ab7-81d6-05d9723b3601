import { useQuery } from "@tanstack/react-query";
import { getHiddenSettings } from "api";
import { useMemo } from "react";

// 隐藏设置类型定义
export interface HiddenSettings {
  specialClient?: number; // 特殊客户标识，2表示天赐
  [key: string]: any; // 其他可能的配置项
}

export const useHiddenSettings = (): HiddenSettings => {
  const { data } = useQuery({
    queryKey: ["getHiddenSettings"],
    queryFn: () => {
      return getHiddenSettings();
    },
  });

  const hiddenSettings = useMemo(() => {
    return (data?.data || {}) as HiddenSettings;
  }, [data]);

  return hiddenSettings;
};
